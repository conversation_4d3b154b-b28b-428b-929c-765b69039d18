using System;
using System.IO;
using System.Text;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy协议定义和消息处理
    /// </summary>
    public static class ScrcpyProtocol
    {
        // 协议版本
        public const string PROTOCOL_VERSION = "3.1";
        
        // 默认端口
        public const int DEFAULT_PORT = 27183;
        
        // 消息类型
        public enum MessageType : byte
        {
            INJECT_KEYCODE = 0,
            INJECT_TEXT = 1,
            INJECT_TOUCH_EVENT = 2,
            INJECT_SCROLL_EVENT = 3,
            BACK_OR_SCREEN_ON = 4,
            EXPAND_NOTIFICATION_PANEL = 5,
            EXPAND_SETTINGS_PANEL = 6,
            COLLAPSE_PANELS = 7,
            GET_CLIPBOARD = 8,
            SET_CLIPBOARD = 9,
            SET_SCREEN_POWER_MODE = 10,
            ROTATE_DEVICE = 11
        }

        // 触摸事件类型
        public enum TouchAction : byte
        {
            DOWN = 0,
            UP = 1,
            MOVE = 2
        }

        // 按键事件类型
        public enum KeyAction : byte
        {
            DOWN = 0,
            UP = 1
        }

        // 屏幕电源模式
        public enum ScreenPowerMode : byte
        {
            OFF = 0,
            NORMAL = 2
        }

        /// <summary>
        /// 创建触摸事件消息
        /// </summary>
        public static byte[] CreateTouchMessage(TouchAction action, long pointerId, float x, float y, int screenWidth, int screenHeight, float pressure = 1.0f)
        {
            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                writer.Write((byte)MessageType.INJECT_TOUCH_EVENT);
                writer.Write((byte)action);
                writer.Write(pointerId);
                
                // 坐标转换为设备坐标
                uint deviceX = (uint)(x * 0xFFFF / screenWidth);
                uint deviceY = (uint)(y * 0xFFFF / screenHeight);
                
                writer.Write(ReverseBytes(deviceX));
                writer.Write(ReverseBytes(deviceY));
                writer.Write(ReverseBytes((ushort)(screenWidth)));
                writer.Write(ReverseBytes((ushort)(screenHeight)));
                writer.Write(ReverseBytes((ushort)(pressure * 0xFFFF)));
                writer.Write(ReverseBytes((uint)1)); // buttons
                
                return stream.ToArray();
            }
        }

        /// <summary>
        /// 创建按键事件消息
        /// </summary>
        public static byte[] CreateKeyMessage(KeyAction action, int keyCode, int metaState = 0)
        {
            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                writer.Write((byte)MessageType.INJECT_KEYCODE);
                writer.Write((byte)action);
                writer.Write(ReverseBytes((uint)keyCode));
                writer.Write(ReverseBytes((uint)0)); // repeat
                writer.Write(ReverseBytes((uint)metaState));
                
                return stream.ToArray();
            }
        }

        /// <summary>
        /// 创建文本输入消息
        /// </summary>
        public static byte[] CreateTextMessage(string text)
        {
            var textBytes = Encoding.UTF8.GetBytes(text);
            using (var stream = new MemoryStream())
            using (var writer = new BinaryWriter(stream))
            {
                writer.Write((byte)MessageType.INJECT_TEXT);
                writer.Write(ReverseBytes((uint)textBytes.Length));
                writer.Write(textBytes);
                
                return stream.ToArray();
            }
        }

        /// <summary>
        /// 创建屏幕电源模式消息
        /// </summary>
        public static byte[] CreateScreenPowerMessage(ScreenPowerMode mode)
        {
            return new byte[] { (byte)MessageType.SET_SCREEN_POWER_MODE, (byte)mode };
        }

        /// <summary>
        /// 创建返回键或屏幕唤醒消息
        /// </summary>
        public static byte[] CreateBackOrScreenOnMessage()
        {
            return new byte[] { (byte)MessageType.BACK_OR_SCREEN_ON };
        }

        /// <summary>
        /// 字节序转换（大端序）
        /// </summary>
        private static uint ReverseBytes(uint value)
        {
            return (value & 0x000000FFU) << 24 | (value & 0x0000FF00U) << 8 |
                   (value & 0x00FF0000U) >> 8 | (value & 0xFF000000U) >> 24;
        }

        private static ushort ReverseBytes(ushort value)
        {
            return (ushort)((value & 0x00FF) << 8 | (value & 0xFF00) >> 8);
        }

        /// <summary>
        /// 解析设备元数据
        /// </summary>
        public static (string deviceName, int width, int height) ParseDeviceMeta(byte[] data)
        {
            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream))
            {
                var nameLength = ReverseBytes(reader.ReadUInt32());
                var nameBytes = reader.ReadBytes((int)nameLength);
                var deviceName = Encoding.UTF8.GetString(nameBytes);
                
                var width = ReverseBytes(reader.ReadUInt16());
                var height = ReverseBytes(reader.ReadUInt16());
                
                return (deviceName, width, height);
            }
        }

        /// <summary>
        /// 生成scrcpy服务器启动参数
        /// </summary>
        public static string GenerateServerArgs(int maxSize = 1080, int bitRate = 2000000, bool audio = false, int scid = 0x17f3)
        {
            return $"video_bit_rate={bitRate} log_level=verbose max_size={maxSize} capture_orientation=0 audio={audio.ToString().ToLower()} scid={scid:x8}";
        }
    }

    /// <summary>
    /// Android按键码定义
    /// </summary>
    public static class AndroidKeyCode
    {
        public const int KEYCODE_BACK = 4;
        public const int KEYCODE_HOME = 3;
        public const int KEYCODE_MENU = 82;
        public const int KEYCODE_POWER = 26;
        public const int KEYCODE_VOLUME_UP = 24;
        public const int KEYCODE_VOLUME_DOWN = 25;
        public const int KEYCODE_ENTER = 66;
        public const int KEYCODE_DEL = 67;
        public const int KEYCODE_TAB = 61;
        public const int KEYCODE_SPACE = 62;
        
        // 字母键
        public const int KEYCODE_A = 29;
        public const int KEYCODE_B = 30;
        public const int KEYCODE_C = 31;
        public const int KEYCODE_D = 32;
        public const int KEYCODE_E = 33;
        public const int KEYCODE_F = 34;
        public const int KEYCODE_G = 35;
        public const int KEYCODE_H = 36;
        public const int KEYCODE_I = 37;
        public const int KEYCODE_J = 38;
        public const int KEYCODE_K = 39;
        public const int KEYCODE_L = 40;
        public const int KEYCODE_M = 41;
        public const int KEYCODE_N = 42;
        public const int KEYCODE_O = 43;
        public const int KEYCODE_P = 44;
        public const int KEYCODE_Q = 45;
        public const int KEYCODE_R = 46;
        public const int KEYCODE_S = 47;
        public const int KEYCODE_T = 48;
        public const int KEYCODE_U = 49;
        public const int KEYCODE_V = 50;
        public const int KEYCODE_W = 51;
        public const int KEYCODE_X = 52;
        public const int KEYCODE_Y = 53;
        public const int KEYCODE_Z = 54;
        
        // 数字键
        public const int KEYCODE_0 = 7;
        public const int KEYCODE_1 = 8;
        public const int KEYCODE_2 = 9;
        public const int KEYCODE_3 = 10;
        public const int KEYCODE_4 = 11;
        public const int KEYCODE_5 = 12;
        public const int KEYCODE_6 = 13;
        public const int KEYCODE_7 = 14;
        public const int KEYCODE_8 = 15;
        public const int KEYCODE_9 = 16;
    }
}
