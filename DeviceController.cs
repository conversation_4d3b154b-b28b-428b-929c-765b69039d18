using System;
using System.IO;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// 设备控制器，用于发送触摸、按键等控制命令
    /// </summary>
    public class DeviceController : IDisposable
    {
        private NetworkStream _controlStream;
        private bool _disposed;
        private int _screenWidth;
        private int _screenHeight;

        /// <summary>
        /// 屏幕宽度
        /// </summary>
        public int ScreenWidth => _screenWidth;

        /// <summary>
        /// 屏幕高度
        /// </summary>
        public int ScreenHeight => _screenHeight;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _controlStream != null && !_disposed;

        /// <summary>
        /// 设置控制流
        /// </summary>
        /// <param name="controlStream">控制数据流</param>
        /// <param name="screenWidth">屏幕宽度</param>
        /// <param name="screenHeight">屏幕高度</param>
        public void SetControlStream(NetworkStream controlStream, int screenWidth, int screenHeight)
        {
            _controlStream = controlStream;
            _screenWidth = screenWidth;
            _screenHeight = screenHeight;
            Console.WriteLine($"设备控制器已连接，屏幕尺寸: {screenWidth}x{screenHeight}");
        }

        /// <summary>
        /// 发送触摸按下事件
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="pointerId">触摸点ID</param>
        public async Task TouchDownAsync(float x, float y, long pointerId = 0)
        {
            await SendTouchEventAsync(ScrcpyProtocol.TouchAction.DOWN, x, y, pointerId);
        }

        /// <summary>
        /// 发送触摸移动事件
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="pointerId">触摸点ID</param>
        public async Task TouchMoveAsync(float x, float y, long pointerId = 0)
        {
            await SendTouchEventAsync(ScrcpyProtocol.TouchAction.MOVE, x, y, pointerId);
        }

        /// <summary>
        /// 发送触摸抬起事件
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="pointerId">触摸点ID</param>
        public async Task TouchUpAsync(float x, float y, long pointerId = 0)
        {
            await SendTouchEventAsync(ScrcpyProtocol.TouchAction.UP, x, y, pointerId);
        }

        /// <summary>
        /// 发送点击事件（按下+抬起）
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="duration">按下持续时间（毫秒）</param>
        public async Task ClickAsync(float x, float y, int duration = 50)
        {
            await TouchDownAsync(x, y);
            if (duration > 0)
            {
                await Task.Delay(duration);
            }
            await TouchUpAsync(x, y);
        }

        /// <summary>
        /// 发送滑动事件
        /// </summary>
        /// <param name="startX">起始X坐标</param>
        /// <param name="startY">起始Y坐标</param>
        /// <param name="endX">结束X坐标</param>
        /// <param name="endY">结束Y坐标</param>
        /// <param name="duration">滑动持续时间（毫秒）</param>
        /// <param name="steps">滑动步数</param>
        public async Task SwipeAsync(float startX, float startY, float endX, float endY, int duration = 300, int steps = 10)
        {
            await TouchDownAsync(startX, startY);

            if (steps > 1)
            {
                float stepX = (endX - startX) / (steps - 1);
                float stepY = (endY - startY) / (steps - 1);
                int stepDelay = duration / steps;

                for (int i = 1; i < steps - 1; i++)
                {
                    await Task.Delay(stepDelay);
                    float currentX = startX + stepX * i;
                    float currentY = startY + stepY * i;
                    await TouchMoveAsync(currentX, currentY);
                }
            }

            await Task.Delay(duration / steps);
            await TouchUpAsync(endX, endY);
        }

        /// <summary>
        /// 发送按键按下事件
        /// </summary>
        /// <param name="keyCode">按键码</param>
        public async Task KeyDownAsync(int keyCode)
        {
            await SendKeyEventAsync(ScrcpyProtocol.KeyAction.DOWN, keyCode);
        }

        /// <summary>
        /// 发送按键抬起事件
        /// </summary>
        /// <param name="keyCode">按键码</param>
        public async Task KeyUpAsync(int keyCode)
        {
            await SendKeyEventAsync(ScrcpyProtocol.KeyAction.UP, keyCode);
        }

        /// <summary>
        /// 发送按键事件（按下+抬起）
        /// </summary>
        /// <param name="keyCode">按键码</param>
        /// <param name="duration">按下持续时间（毫秒）</param>
        public async Task PressKeyAsync(int keyCode, int duration = 50)
        {
            await KeyDownAsync(keyCode);
            if (duration > 0)
            {
                await Task.Delay(duration);
            }
            await KeyUpAsync(keyCode);
        }

        /// <summary>
        /// 发送文本输入
        /// </summary>
        /// <param name="text">要输入的文本</param>
        public async Task InputTextAsync(string text)
        {
            if (string.IsNullOrEmpty(text)) return;

            var message = ScrcpyProtocol.CreateTextMessage(text);
            await SendMessageAsync(message);
        }

        /// <summary>
        /// 发送返回键
        /// </summary>
        public async Task PressBackAsync()
        {
            await PressKeyAsync(AndroidKeyCode.KEYCODE_BACK);
        }

        /// <summary>
        /// 发送Home键
        /// </summary>
        public async Task PressHomeAsync()
        {
            await PressKeyAsync(AndroidKeyCode.KEYCODE_HOME);
        }

        /// <summary>
        /// 发送菜单键
        /// </summary>
        public async Task PressMenuAsync()
        {
            await PressKeyAsync(AndroidKeyCode.KEYCODE_MENU);
        }

        /// <summary>
        /// 发送电源键
        /// </summary>
        public async Task PressPowerAsync()
        {
            await PressKeyAsync(AndroidKeyCode.KEYCODE_POWER);
        }

        /// <summary>
        /// 设置屏幕电源模式
        /// </summary>
        /// <param name="mode">电源模式</param>
        public async Task SetScreenPowerModeAsync(ScrcpyProtocol.ScreenPowerMode mode)
        {
            var message = ScrcpyProtocol.CreateScreenPowerMessage(mode);
            await SendMessageAsync(message);
        }

        /// <summary>
        /// 唤醒屏幕或发送返回键
        /// </summary>
        public async Task BackOrScreenOnAsync()
        {
            var message = ScrcpyProtocol.CreateBackOrScreenOnMessage();
            await SendMessageAsync(message);
        }

        /// <summary>
        /// 发送触摸事件
        /// </summary>
        private async Task SendTouchEventAsync(ScrcpyProtocol.TouchAction action, float x, float y, long pointerId)
        {
            if (!IsConnected) return;

            var message = ScrcpyProtocol.CreateTouchMessage(action, pointerId, x, y, _screenWidth, _screenHeight);
            await SendMessageAsync(message);
        }

        /// <summary>
        /// 发送按键事件
        /// </summary>
        private async Task SendKeyEventAsync(ScrcpyProtocol.KeyAction action, int keyCode)
        {
            if (!IsConnected) return;

            var message = ScrcpyProtocol.CreateKeyMessage(action, keyCode);
            await SendMessageAsync(message);
        }

        /// <summary>
        /// 发送消息到设备
        /// </summary>
        private async Task SendMessageAsync(byte[] message)
        {
            if (!IsConnected || message == null || message.Length == 0) return;

            try
            {
                await _controlStream.WriteAsync(message, 0, message.Length);
                await _controlStream.FlushAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送控制消息失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _controlStream = null;
            _disposed = true;
        }
    }
}
