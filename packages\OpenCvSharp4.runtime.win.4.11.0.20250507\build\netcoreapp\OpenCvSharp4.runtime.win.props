﻿<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<PropertyGroup>
		<OpenCvSharp4ExternalNativeDlls>$(MSBuildThisFileDirectory)..\..\runtimes</OpenCvSharp4ExternalNativeDlls>
	</PropertyGroup>
    <ItemGroup Condition="$(TargetFrameworkVersion.StartsWith('v4')) Or $(TargetFramework.StartsWith('net4'))">
		<Content Include="$(OpenCvSharp4ExternalNativeDlls)\win-x86\native\OpenCvSharpExtern.dll">
			<Link>dll\x86\OpenCvSharpExtern.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="$(OpenCvSharp4ExternalNativeDlls)\win-x86\native\opencv_videoio_ffmpeg4110.dll">
			<Link>dll\x86\opencv_videoio_ffmpeg4110.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup Condition="$(TargetFrameworkVersion.StartsWith('v4')) Or $(TargetFramework.StartsWith('net4'))">
		<Content Include="$(OpenCvSharp4ExternalNativeDlls)\win-x64\native\OpenCvSharpExtern.dll">
			<Link>dll\x64\OpenCvSharpExtern.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="$(OpenCvSharp4ExternalNativeDlls)\win-x64\native\opencv_videoio_ffmpeg4110_64.dll">
			<Link>dll\x64\opencv_videoio_ffmpeg4110_64.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
</Project>
