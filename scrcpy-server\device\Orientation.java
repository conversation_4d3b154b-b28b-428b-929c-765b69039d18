package com.genymobile.scrcpy.device;

public enum Orientation {

    // @formatter:off
    Orient0("0"),
    <PERSON><PERSON>("90"),
    Orient180("180"),
    <PERSON>270("270"),
    <PERSON>lip0("flip0"),
    <PERSON><PERSON><PERSON>("flip90"),
    <PERSON>lip180("flip180"),
    <PERSON>lip270("flip270");

    public enum Lock {
        Unlocked, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    }

    private final String name;

    Orientation(String name) {
        this.name = name;
    }

    public static Orientation getByName(String name) {
        for (Orientation orientation : values()) {
            if (orientation.name.equals(name)) {
                return orientation;
            }
        }

        throw new IllegalArgumentException("Unknown orientation: " + name);
    }

    public static Orientation fromRotation(int rotation) {
        assert rotation >= 0 && rotation < 4;
        return values()[rotation];
    }

    public boolean isFlipped() {
        return (ordinal() & 4) != 0;
    }

    public int getRotation() {
        return ordinal() & 3;
    }
}
