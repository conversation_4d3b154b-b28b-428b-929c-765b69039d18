using System;
using System.Drawing;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy客户端使用示例
    /// </summary>
    public class ScrcpyExample
    {
        private ScrcpyClient _client;

        public ScrcpyExample()
        {
            _client = new ScrcpyClient();
            SetupEventHandlers();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            _client.Connected += OnDeviceConnected;
            _client.Disconnected += OnDeviceDisconnected;
            _client.Error += OnError;
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId = null)
        {
            Console.WriteLine("正在连接Android设备...");
            
            return await _client.ConnectAsync(
                deviceId: deviceId,
                maxSize: 1080,
                bitRate: 2000000
            );
        }

        /// <summary>
        /// 获取当前屏幕截图
        /// </summary>
        public Bitmap GetScreenshot()
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            var frameBuffer = _client.VideoDecoder.FrameBuffer;
            if (!frameBuffer.HasFrame)
            {
                return null;
            }

            return frameBuffer.GetCurrentFrame();
        }

        /// <summary>
        /// 保存屏幕截图
        /// </summary>
        public bool SaveScreenshot(string filePath)
        {
            try
            {
                var frameBuffer = _client.VideoDecoder.FrameBuffer;
                if (!frameBuffer.HasFrame)
                {
                    return false;
                }

                frameBuffer.SaveCurrentFrame(filePath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存截图失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 点击屏幕指定位置
        /// </summary>
        public async Task ClickAsync(int x, int y)
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            await _client.DeviceController.ClickAsync(x, y);
        }

        /// <summary>
        /// 滑动屏幕
        /// </summary>
        public async Task SwipeAsync(int startX, int startY, int endX, int endY, int duration = 300)
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            await _client.DeviceController.SwipeAsync(startX, startY, endX, endY, duration);
        }

        /// <summary>
        /// 输入文本
        /// </summary>
        public async Task InputTextAsync(string text)
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            await _client.DeviceController.InputTextAsync(text);
        }

        /// <summary>
        /// 按返回键
        /// </summary>
        public async Task PressBackAsync()
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            await _client.DeviceController.PressBackAsync();
        }

        /// <summary>
        /// 按Home键
        /// </summary>
        public async Task PressHomeAsync()
        {
            if (!_client.IsConnected)
            {
                throw new InvalidOperationException("设备未连接");
            }

            await _client.DeviceController.PressHomeAsync();
        }

        /// <summary>
        /// 等待指定颜色出现在指定位置
        /// </summary>
        public async Task<bool> WaitForColorAsync(int x, int y, Color expectedColor, int timeoutMs = 5000)
        {
            var startTime = DateTime.Now;
            
            while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
            {
                using (var screenshot = GetScreenshot())
                {
                    if (screenshot != null && x < screenshot.Width && y < screenshot.Height)
                    {
                        var pixelColor = screenshot.GetPixel(x, y);
                        if (ColorsMatch(pixelColor, expectedColor, tolerance: 10))
                        {
                            return true;
                        }
                    }
                }
                
                await Task.Delay(100);
            }
            
            return false;
        }

        /// <summary>
        /// 查找屏幕上的颜色位置
        /// </summary>
        public Point? FindColor(Color targetColor, int tolerance = 10)
        {
            using (var screenshot = GetScreenshot())
            {
                if (screenshot == null) return null;

                for (int y = 0; y < screenshot.Height; y++)
                {
                    for (int x = 0; x < screenshot.Width; x++)
                    {
                        var pixelColor = screenshot.GetPixel(x, y);
                        if (ColorsMatch(pixelColor, targetColor, tolerance))
                        {
                            return new Point(x, y);
                        }
                    }
                }
            }
            
            return null;
        }

        /// <summary>
        /// 自动游戏示例：简单的点击游戏
        /// </summary>
        public async Task AutoGameExampleAsync()
        {
            if (!_client.IsConnected)
            {
                Console.WriteLine("设备未连接，无法执行自动游戏");
                return;
            }

            Console.WriteLine("开始自动游戏示例...");

            try
            {
                // 示例：查找红色按钮并点击
                var redButtonColor = Color.FromArgb(255, 0, 0); // 红色
                var buttonPosition = FindColor(redButtonColor, tolerance: 20);
                
                if (buttonPosition.HasValue)
                {
                    Console.WriteLine($"找到红色按钮在位置: {buttonPosition.Value}");
                    await ClickAsync(buttonPosition.Value.X, buttonPosition.Value.Y);
                    await Task.Delay(1000);
                }

                // 示例：滑动操作
                Console.WriteLine("执行滑动操作...");
                await SwipeAsync(
                    startX: _client.ScreenWidth / 4,
                    startY: _client.ScreenHeight / 2,
                    endX: _client.ScreenWidth * 3 / 4,
                    endY: _client.ScreenHeight / 2,
                    duration: 500
                );

                Console.WriteLine("自动游戏示例完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"自动游戏执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 颜色匹配检查
        /// </summary>
        private bool ColorsMatch(Color color1, Color color2, int tolerance)
        {
            return Math.Abs(color1.R - color2.R) <= tolerance &&
                   Math.Abs(color1.G - color2.G) <= tolerance &&
                   Math.Abs(color1.B - color2.B) <= tolerance;
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (_client != null)
            {
                await _client.DisconnectAsync();
            }
        }

        /// <summary>
        /// 设备连接事件处理
        /// </summary>
        private void OnDeviceConnected(object sender, string deviceName)
        {
            Console.WriteLine($"✓ 设备已连接: {deviceName}");
            Console.WriteLine($"  屏幕尺寸: {_client.ScreenWidth}x{_client.ScreenHeight}");
        }

        /// <summary>
        /// 设备断开连接事件处理
        /// </summary>
        private void OnDeviceDisconnected(object sender, EventArgs e)
        {
            Console.WriteLine("✗ 设备已断开连接");
        }

        /// <summary>
        /// 错误事件处理
        /// </summary>
        private void OnError(object sender, string error)
        {
            Console.WriteLine($"✗ 错误: {error}");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        public (bool isConnected, string deviceName, int width, int height) GetDeviceInfo()
        {
            return (_client.IsConnected, _client.DeviceName, _client.ScreenWidth, _client.ScreenHeight);
        }

        /// <summary>
        /// 获取最新帧信息
        /// </summary>
        public (long frameNumber, DateTime lastUpdate, bool hasFrame) GetFrameInfo()
        {
            var frameBuffer = _client.VideoDecoder.FrameBuffer;
            var info = frameBuffer.GetFrameInfo();
            return (info.frameNumber, info.lastUpdate, frameBuffer.HasFrame);
        }
    }
}
