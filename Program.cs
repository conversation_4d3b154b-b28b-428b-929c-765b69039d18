﻿using System;
using System.Drawing;
using System.Threading.Tasks;

namespace AutoGame
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Scrcpy客户端演示程序 ===");

            // 创建Scrcpy客户端
            using (var client = new ScrcpyClient())
            {
                // 注册事件
                client.Connected += (sender, deviceName) =>
                {
                    Console.WriteLine($"✓ 已连接到设备: {deviceName}");
                };

                client.Disconnected += (sender, e) =>
                {
                    Console.WriteLine("✗ 设备已断开连接");
                };

                client.Error += (sender, error) =>
                {
                    Console.WriteLine($"✗ 错误: {error}");
                };

                try
                {
                    // 连接到设备
                    Console.WriteLine("正在连接设备...");
                    bool connected = await client.ConnectAsync(
                        deviceId: null,     // 自动选择第一个设备
                        maxSize: 1080,      // 最大分辨率1080p
                        bitRate: 2000000    // 2Mbps比特率
                    );

                    if (!connected)
                    {
                        Console.WriteLine("连接失败！请确保：");
                        Console.WriteLine("1. 设备已连接并启用USB调试");
                        Console.WriteLine("2. adb命令可用");
                        Console.WriteLine("3. scrcpy-server.jar文件存在");
                        return;
                    }

                    // 演示功能
                    await DemoFunctions(client);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"程序异常: {ex.Message}");
                }
                finally
                {
                    Console.WriteLine("正在断开连接...");
                    await client.DisconnectAsync();
                }
            }

            Console.WriteLine("程序结束，按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 演示各种功能
        /// </summary>
        private static async Task DemoFunctions(ScrcpyClient client)
        {
            Console.WriteLine("\n=== 功能演示 ===");

            // 等待视频流稳定
            Console.WriteLine("等待视频流稳定...");
            await Task.Delay(3000);

            // 演示获取最新帧
            await DemoFrameCapture(client);

            // 演示设备控制
            await DemoDeviceControl(client);

            // 持续运行一段时间
            Console.WriteLine("程序将运行30秒，您可以观察视频流和控制效果...");
            await Task.Delay(30000);
        }

        /// <summary>
        /// 演示帧捕获功能
        /// </summary>
        private static async Task DemoFrameCapture(ScrcpyClient client)
        {
            Console.WriteLine("\n--- 帧捕获演示 ---");

            var frameBuffer = client.VideoDecoder.FrameBuffer;

            // 等待第一帧
            int attempts = 0;
            while (!frameBuffer.HasFrame && attempts < 50)
            {
                await Task.Delay(100);
                attempts++;
            }

            if (frameBuffer.HasFrame)
            {
                var frameInfo = frameBuffer.GetFrameInfo();
                Console.WriteLine($"✓ 获取到视频帧:");
                Console.WriteLine($"  - 帧号: {frameInfo.frameNumber}");
                Console.WriteLine($"  - 尺寸: {frameInfo.width}x{frameInfo.height}");
                Console.WriteLine($"  - 更新时间: {frameInfo.lastUpdate}");

                // 保存当前帧到文件
                try
                {
                    frameBuffer.SaveCurrentFrame("screenshot.png");
                    Console.WriteLine("✓ 当前帧已保存为 screenshot.png");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ 保存帧失败: {ex.Message}");
                }

                // 获取帧数据
                var frameData = frameBuffer.GetCurrentFrameData();
                if (frameData != null)
                {
                    Console.WriteLine($"✓ 获取到原始帧数据，大小: {frameData.Length} 字节");
                }
            }
            else
            {
                Console.WriteLine("✗ 未能获取到视频帧");
            }
        }

        /// <summary>
        /// 演示设备控制功能
        /// </summary>
        private static async Task DemoDeviceControl(ScrcpyClient client)
        {
            Console.WriteLine("\n--- 设备控制演示 ---");

            var controller = client.DeviceController;

            if (!controller.IsConnected)
            {
                Console.WriteLine("✗ 设备控制器未连接");
                return;
            }

            Console.WriteLine($"✓ 设备控制器已连接，屏幕尺寸: {controller.ScreenWidth}x{controller.ScreenHeight}");

            try
            {
                // 演示点击
                Console.WriteLine("演示点击屏幕中心...");
                float centerX = controller.ScreenWidth / 2f;
                float centerY = controller.ScreenHeight / 2f;
                await controller.ClickAsync(centerX, centerY);
                await Task.Delay(1000);

                // 演示滑动
                Console.WriteLine("演示从左向右滑动...");
                await controller.SwipeAsync(
                    startX: controller.ScreenWidth * 0.2f,
                    startY: controller.ScreenHeight * 0.5f,
                    endX: controller.ScreenWidth * 0.8f,
                    endY: controller.ScreenHeight * 0.5f,
                    duration: 500
                );
                await Task.Delay(1000);

                // 演示按键
                Console.WriteLine("演示按返回键...");
                await controller.PressBackAsync();
                await Task.Delay(1000);

                // 演示文本输入
                Console.WriteLine("演示文本输入...");
                await controller.InputTextAsync("Hello from Scrcpy Client!");
                await Task.Delay(1000);

                Console.WriteLine("✓ 设备控制演示完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 设备控制演示失败: {ex.Message}");
            }
        }
    }
}
