using System;
using System.Diagnostics;
using System.IO;
using System.Net.NetworkInformation;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy连接诊断工具
    /// </summary>
    public static class ScrcpyDiagnostics
    {
        /// <summary>
        /// 运行完整的诊断检查
        /// </summary>
        public static async Task<bool> RunDiagnosticsAsync()
        {
            Console.WriteLine("=== Scrcpy连接诊断 ===\n");

            bool allPassed = true;

            // 1. 检查ADB
            Console.WriteLine("1. 检查ADB...");
            if (!await CheckAdbAsync())
            {
                allPassed = false;
            }

            // 2. 检查设备连接
            Console.WriteLine("\n2. 检查设备连接...");
            if (!await CheckDeviceConnectionAsync())
            {
                allPassed = false;
            }

            // 3. 检查scrcpy-server.jar
            Console.WriteLine("\n3. 检查scrcpy-server.jar...");
            if (!CheckScrcpyServerJar())
            {
                allPassed = false;
            }

            // 4. 检查端口
            Console.WriteLine("\n4. 检查端口...");
            if (!CheckPorts())
            {
                allPassed = false;
            }

            // 5. 测试端口转发
            Console.WriteLine("\n5. 测试端口转发...");
            if (!await TestPortForwardingAsync())
            {
                allPassed = false;
            }

            Console.WriteLine($"\n=== 诊断完成 ===");
            Console.WriteLine(allPassed ? "✓ 所有检查通过" : "✗ 发现问题，请根据上述提示解决");

            return allPassed;
        }

        /// <summary>
        /// 检查ADB是否可用
        /// </summary>
        private static async Task<bool> CheckAdbAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = "version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine($"✓ ADB可用: {output.Split('\n')[0]}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"✗ ADB错误: {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ADB不可用: {ex.Message}");
                Console.WriteLine("  解决方案: 请安装Android SDK或确保adb在PATH中");
                return false;
            }
        }

        /// <summary>
        /// 检查设备连接
        /// </summary>
        private static async Task<bool> CheckDeviceConnectionAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = "devices",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                process.WaitForExit();

                var lines = output.Split('\n');
                int deviceCount = 0;
                
                foreach (var line in lines)
                {
                    if (line.Contains("\tdevice"))
                    {
                        deviceCount++;
                        Console.WriteLine($"✓ 发现设备: {line.Split('\t')[0]}");
                    }
                    else if (line.Contains("\tunauthorized"))
                    {
                        Console.WriteLine($"✗ 设备未授权: {line.Split('\t')[0]}");
                        Console.WriteLine("  解决方案: 请在设备上允许USB调试");
                        return false;
                    }
                }

                if (deviceCount > 0)
                {
                    Console.WriteLine($"✓ 找到 {deviceCount} 个已连接的设备");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ 没有找到已连接的设备");
                    Console.WriteLine("  解决方案: 请连接Android设备并启用USB调试");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 检查设备连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查scrcpy-server.jar文件
        /// </summary>
        private static bool CheckScrcpyServerJar()
        {
            var jarPath = "scrcpy-server.jar";
            
            if (File.Exists(jarPath))
            {
                var fileInfo = new FileInfo(jarPath);
                Console.WriteLine($"✓ scrcpy-server.jar存在 (大小: {fileInfo.Length} 字节)");
                return true;
            }
            else
            {
                Console.WriteLine("✗ scrcpy-server.jar不存在");
                Console.WriteLine("  解决方案: 请从scrcpy官方仓库下载scrcpy-server.jar文件");
                Console.WriteLine("  下载地址: https://github.com/Genymobile/scrcpy/releases");
                return false;
            }
        }

        /// <summary>
        /// 检查端口是否被占用
        /// </summary>
        private static bool CheckPorts()
        {
            var ports = new[] { 27183, 27184 };
            bool allPortsAvailable = true;

            foreach (var port in ports)
            {
                if (IsPortInUse(port))
                {
                    Console.WriteLine($"✗ 端口 {port} 被占用");
                    allPortsAvailable = false;
                }
                else
                {
                    Console.WriteLine($"✓ 端口 {port} 可用");
                }
            }

            if (!allPortsAvailable)
            {
                Console.WriteLine("  解决方案: 请关闭占用端口的程序或使用其他端口");
            }

            return allPortsAvailable;
        }

        /// <summary>
        /// 检查端口是否被占用
        /// </summary>
        private static bool IsPortInUse(int port)
        {
            try
            {
                var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
                var tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

                foreach (var tcpInfo in tcpConnInfoArray)
                {
                    if (tcpInfo.Port == port)
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 测试端口转发
        /// </summary>
        private static async Task<bool> TestPortForwardingAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = "forward tcp:27183 localabstract:scrcpy",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                process.WaitForExit();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine("✓ 端口转发设置成功");
                    
                    // 清理端口转发
                    var cleanupProcess = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "adb",
                            Arguments = "forward --remove tcp:27183",
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };
                    cleanupProcess.Start();
                    cleanupProcess.WaitForExit();
                    
                    return true;
                }
                else
                {
                    Console.WriteLine($"✗ 端口转发失败: {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试端口转发失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示解决方案建议
        /// </summary>
        public static void ShowSolutions()
        {
            Console.WriteLine("\n=== 常见问题解决方案 ===");
            Console.WriteLine();
            Console.WriteLine("1. 连接被拒绝 (127.0.0.1:27183):");
            Console.WriteLine("   - 确保设备已连接并授权USB调试");
            Console.WriteLine("   - 检查scrcpy-server.jar文件是否存在");
            Console.WriteLine("   - 尝试重新启动adb服务: adb kill-server && adb start-server");
            Console.WriteLine();
            Console.WriteLine("2. 设备未找到:");
            Console.WriteLine("   - 检查USB连接");
            Console.WriteLine("   - 启用开发者选项和USB调试");
            Console.WriteLine("   - 在设备上允许USB调试授权");
            Console.WriteLine();
            Console.WriteLine("3. 端口被占用:");
            Console.WriteLine("   - 关闭其他scrcpy实例");
            Console.WriteLine("   - 使用不同的端口号");
            Console.WriteLine("   - 重启计算机");
            Console.WriteLine();
            Console.WriteLine("4. scrcpy-server.jar缺失:");
            Console.WriteLine("   - 从官方仓库下载: https://github.com/Genymobile/scrcpy/releases");
            Console.WriteLine("   - 确保文件放在程序根目录");
        }
    }
}
