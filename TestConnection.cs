using System;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// 简单的连接测试程序
    /// </summary>
    public class TestConnection
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== Scrcpy连接测试 ===\n");

            try
            {
                // 运行诊断
                Console.WriteLine("1. 运行连接诊断...");
                bool diagnosticsPassed = await ScrcpyDiagnostics.RunDiagnosticsAsync();
                
                if (!diagnosticsPassed)
                {
                    Console.WriteLine("\n诊断发现问题，请先解决这些问题再继续。");
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                    return;
                }

                Console.WriteLine("\n2. 尝试连接设备...");
                
                // 创建客户端
                using (var client = new ScrcpyClient())
                {
                    // 设置事件处理
                    client.Connected += (sender, deviceName) =>
                    {
                        Console.WriteLine($"✓ 成功连接到设备: {deviceName}");
                    };
                    
                    client.Error += (sender, error) =>
                    {
                        Console.WriteLine($"✗ 连接错误: {error}");
                    };

                    // 尝试连接
                    bool connected = await client.ConnectAsync(
                        deviceId: null,
                        maxSize: 720,      // 使用较低分辨率进行测试
                        bitRate: 1000000   // 使用较低比特率
                    );

                    if (connected)
                    {
                        Console.WriteLine("\n✓ 连接成功！");
                        Console.WriteLine($"设备名称: {client.DeviceName}");
                        Console.WriteLine($"屏幕尺寸: {client.ScreenWidth}x{client.ScreenHeight}");
                        
                        // 等待一段时间以接收视频数据
                        Console.WriteLine("\n等待视频数据...");
                        await Task.Delay(5000);
                        
                        // 检查是否收到视频帧
                        var frameBuffer = client.VideoDecoder.FrameBuffer;
                        if (frameBuffer.HasFrame)
                        {
                            var frameInfo = frameBuffer.GetFrameInfo();
                            Console.WriteLine($"✓ 已接收视频帧: 第{frameInfo.frameNumber}帧");
                            Console.WriteLine($"  尺寸: {frameInfo.width}x{frameInfo.height}");
                            Console.WriteLine($"  最后更新: {frameInfo.lastUpdate}");
                            
                            // 尝试保存截图
                            try
                            {
                                frameBuffer.SaveCurrentFrame("test_screenshot.png");
                                Console.WriteLine("✓ 测试截图已保存为 test_screenshot.png");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"✗ 保存截图失败: {ex.Message}");
                            }
                        }
                        else
                        {
                            Console.WriteLine("✗ 未接收到视频帧");
                        }
                        
                        // 测试设备控制
                        if (client.DeviceController.IsConnected)
                        {
                            Console.WriteLine("\n测试设备控制...");
                            try
                            {
                                // 简单的点击测试（点击屏幕中心）
                                float centerX = client.ScreenWidth / 2f;
                                float centerY = client.ScreenHeight / 2f;
                                await client.DeviceController.ClickAsync(centerX, centerY);
                                Console.WriteLine("✓ 设备控制测试成功");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"✗ 设备控制测试失败: {ex.Message}");
                            }
                        }
                        else
                        {
                            Console.WriteLine("✗ 设备控制器未连接");
                        }
                        
                        Console.WriteLine("\n连接测试完成。按任意键断开连接...");
                        Console.ReadKey();
                        
                        await client.DisconnectAsync();
                    }
                    else
                    {
                        Console.WriteLine("\n✗ 连接失败");
                        Console.WriteLine("\n可能的解决方案:");
                        Console.WriteLine("1. 确保设备已连接并启用USB调试");
                        Console.WriteLine("2. 检查scrcpy-server.jar文件是否存在");
                        Console.WriteLine("3. 尝试重启adb服务: adb kill-server && adb start-server");
                        Console.WriteLine("4. 检查设备是否授权了USB调试");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n程序异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n测试结束，按任意键退出...");
            Console.ReadKey();
        }
    }
}
