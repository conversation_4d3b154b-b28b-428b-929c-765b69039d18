using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Threading;

namespace AutoGame
{
    /// <summary>
    /// 帧缓冲管理器，用于存储和管理最新的视频帧
    /// </summary>
    public class FrameBuffer : IDisposable
    {
        private readonly object _lock = new object();
        private Bitmap _currentFrame;
        private byte[] _frameData;
        private int _width;
        private int _height;
        private long _frameNumber;
        private DateTime _lastUpdateTime;
        private bool _disposed;

        /// <summary>
        /// 当前帧宽度
        /// </summary>
        public int Width
        {
            get
            {
                lock (_lock)
                {
                    return _width;
                }
            }
        }

        /// <summary>
        /// 当前帧高度
        /// </summary>
        public int Height
        {
            get
            {
                lock (_lock)
                {
                    return _height;
                }
            }
        }

        /// <summary>
        /// 当前帧号
        /// </summary>
        public long FrameNumber
        {
            get
            {
                lock (_lock)
                {
                    return _frameNumber;
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get
            {
                lock (_lock)
                {
                    return _lastUpdateTime;
                }
            }
        }

        /// <summary>
        /// 是否有可用帧
        /// </summary>
        public bool HasFrame
        {
            get
            {
                lock (_lock)
                {
                    return _currentFrame != null;
                }
            }
        }

        /// <summary>
        /// 更新帧数据（RGB24格式）
        /// </summary>
        /// <param name="data">RGB24格式的图像数据</param>
        /// <param name="width">图像宽度</param>
        /// <param name="height">图像高度</param>
        public void UpdateFrame(byte[] data, int width, int height)
        {
            if (_disposed) return;

            lock (_lock)
            {
                try
                {
                    // 释放旧的帧
                    _currentFrame?.Dispose();

                    // 创建新的Bitmap
                    _currentFrame = new Bitmap(width, height, PixelFormat.Format24bppRgb);
                    
                    // 锁定位图数据
                    var bmpData = _currentFrame.LockBits(
                        new Rectangle(0, 0, width, height),
                        ImageLockMode.WriteOnly,
                        PixelFormat.Format24bppRgb);

                    try
                    {
                        // 复制数据到位图
                        int stride = bmpData.Stride;
                        int bytesPerPixel = 3; // RGB24
                        
                        for (int y = 0; y < height; y++)
                        {
                            IntPtr row = bmpData.Scan0 + (y * stride);
                            int sourceOffset = y * width * bytesPerPixel;
                            Marshal.Copy(data, sourceOffset, row, width * bytesPerPixel);
                        }
                    }
                    finally
                    {
                        _currentFrame.UnlockBits(bmpData);
                    }

                    // 更新元数据
                    _width = width;
                    _height = height;
                    _frameNumber++;
                    _lastUpdateTime = DateTime.Now;
                    
                    // 保存原始数据副本
                    if (_frameData == null || _frameData.Length != data.Length)
                    {
                        _frameData = new byte[data.Length];
                    }
                    Array.Copy(data, _frameData, data.Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"更新帧数据时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取当前帧的副本
        /// </summary>
        /// <returns>当前帧的Bitmap副本，如果没有帧则返回null</returns>
        public Bitmap GetCurrentFrame()
        {
            lock (_lock)
            {
                if (_currentFrame == null) return null;

                try
                {
                    return new Bitmap(_currentFrame);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取当前帧时发生错误: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// 获取当前帧的原始数据副本
        /// </summary>
        /// <returns>RGB24格式的原始数据副本</returns>
        public byte[] GetCurrentFrameData()
        {
            lock (_lock)
            {
                if (_frameData == null) return null;

                var copy = new byte[_frameData.Length];
                Array.Copy(_frameData, copy, _frameData.Length);
                return copy;
            }
        }

        /// <summary>
        /// 将当前帧保存到文件
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <param name="format">图像格式</param>
        public void SaveCurrentFrame(string filePath, ImageFormat format = null)
        {
            format = format ?? ImageFormat.Png;

            lock (_lock)
            {
                if (_currentFrame == null)
                {
                    throw new InvalidOperationException("没有可用的帧数据");
                }

                try
                {
                    _currentFrame.Save(filePath, format);
                }
                catch (Exception ex)
                {
                    throw new Exception($"保存帧到文件时发生错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 清空帧缓冲
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                _currentFrame?.Dispose();
                _currentFrame = null;
                _frameData = null;
                _width = 0;
                _height = 0;
                _frameNumber = 0;
                _lastUpdateTime = default;
            }
        }

        /// <summary>
        /// 获取帧统计信息
        /// </summary>
        public (long frameNumber, DateTime lastUpdate, int width, int height) GetFrameInfo()
        {
            lock (_lock)
            {
                return (_frameNumber, _lastUpdateTime, _width, _height);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            lock (_lock)
            {
                _currentFrame?.Dispose();
                _currentFrame = null;
                _frameData = null;
                _disposed = true;
            }
        }
    }
}
