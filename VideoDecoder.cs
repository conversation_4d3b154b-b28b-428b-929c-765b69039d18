using System;
using System.Runtime.InteropServices;
using FFmpeg.AutoGen;

namespace AutoGame
{
    /// <summary>
    /// 使用FFmpeg.AutoGen的视频解码器
    /// </summary>
    public unsafe class VideoDecoder : IDisposable
    {
        private AVCodecContext* _codecContext;
        private AVCodec* _codec;
        private AVFrame* _frame;
        private AVFrame* _frameRGB;
        private SwsContext* _swsContext;
        private byte[] _buffer;
        private bool _disposed;
        private readonly FrameBuffer _frameBuffer;

        /// <summary>
        /// 视频宽度
        /// </summary>
        public int Width { get; private set; }

        /// <summary>
        /// 视频高度
        /// </summary>
        public int Height { get; private set; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }

        /// <summary>
        /// 帧缓冲器
        /// </summary>
        public FrameBuffer FrameBuffer => _frameBuffer;

        public VideoDecoder()
        {
            _frameBuffer = new FrameBuffer();
            InitializeFFmpeg();
        }

        /// <summary>
        /// 初始化FFmpeg
        /// </summary>
        private void InitializeFFmpeg()
        {
            try
            {
                // 设置FFmpeg库路径
                string ffmpegPath = @".\ffmpeg";
                if (System.IO.Directory.Exists(ffmpegPath))
                {
                    ffmpeg.RootPath = ffmpegPath;
                    Console.WriteLine($"FFmpeg库路径设置为: {ffmpegPath}");
                }
                else
                {
                    Console.WriteLine("FFmpeg库路径不存在，使用系统默认路径");
                }

                // 测试FFmpeg是否可用
                try
                {
                    // 简单测试FFmpeg是否可用
                    Console.WriteLine("FFmpeg库检查完成");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"FFmpeg库加载失败: {ex.Message}");
                    Console.WriteLine("将使用简化模式（不进行视频解码）");
                }

                Console.WriteLine("FFmpeg初始化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FFmpeg初始化失败: {ex.Message}");
                Console.WriteLine("将使用简化模式（不进行视频解码）");
            }
        }

        /// <summary>
        /// 初始化H264解码器
        /// </summary>
        public bool InitializeDecoder(AVCodecID codecId = AVCodecID.AV_CODEC_ID_H264)
        {
            try
            {
                Console.WriteLine("开始初始化视频解码器...");

                // 测试FFmpeg基本功能
                try
                {
                    // 简单测试FFmpeg是否可用
                    Console.WriteLine("FFmpeg基本功能检查完成");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"FFmpeg不可用: {ex.Message}");
                    Console.WriteLine("使用简化模式，跳过视频解码");
                    IsInitialized = true; // 设置为已初始化，但不进行实际解码
                    return true;
                }

                // 查找解码器
                Console.WriteLine($"查找解码器: {codecId}");
                _codec = ffmpeg.avcodec_find_decoder(codecId);
                if (_codec == null)
                {
                    Console.WriteLine($"找不到解码器: {codecId}");
                    Console.WriteLine("使用简化模式，跳过视频解码");
                    IsInitialized = true;
                    return true;
                }

                Console.WriteLine("解码器查找成功");

                // 分配解码器上下文
                Console.WriteLine("分配解码器上下文...");
                _codecContext = ffmpeg.avcodec_alloc_context3(_codec);
                if (_codecContext == null)
                {
                    Console.WriteLine("无法分配解码器上下文");
                    Console.WriteLine("使用简化模式，跳过视频解码");
                    IsInitialized = true;
                    return true;
                }

                // 打开解码器
                Console.WriteLine("打开解码器...");
                var result = ffmpeg.avcodec_open2(_codecContext, _codec, null);
                if (result < 0)
                {
                    Console.WriteLine($"无法打开解码器: {result}");
                    Console.WriteLine("使用简化模式，跳过视频解码");
                    IsInitialized = true;
                    return true;
                }

                // 分配帧
                Console.WriteLine("分配视频帧...");
                _frame = ffmpeg.av_frame_alloc();
                _frameRGB = ffmpeg.av_frame_alloc();

                if (_frame == null || _frameRGB == null)
                {
                    Console.WriteLine("无法分配帧");
                    Console.WriteLine("使用简化模式，跳过视频解码");
                    IsInitialized = true;
                    return true;
                }

                IsInitialized = true;
                Console.WriteLine($"✓ 解码器初始化成功: {codecId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化解码器时发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
                Console.WriteLine("使用简化模式，跳过视频解码");
                IsInitialized = true; // 即使失败也设置为已初始化，允许连接继续
                return true;
            }
        }

        /// <summary>
        /// 解码视频数据
        /// </summary>
        /// <param name="data">编码的视频数据</param>
        /// <param name="size">数据大小</param>
        /// <returns>是否成功解码出新帧</returns>
        public bool DecodeFrame(byte[] data, int size)
        {
            if (!IsInitialized || _disposed)
                return false;

            // 如果FFmpeg不可用，只是接收数据但不解码
            if (_codec == null || _codecContext == null)
            {
                // 简化模式：只记录接收到的数据
                Console.WriteLine($"接收到视频数据: {size} 字节 (简化模式，未解码)");
                return false; // 返回false表示没有新帧
            }

            try
            {
                // 创建数据包
                var packet = ffmpeg.av_packet_alloc();
                if (packet == null)
                    return false;

                try
                {
                    // 设置数据包数据
                    fixed (byte* dataPtr = data)
                    {
                        packet->data = dataPtr;
                        packet->size = size;

                        // 发送数据包到解码器
                        var sendResult = ffmpeg.avcodec_send_packet(_codecContext, packet);
                        if (sendResult < 0)
                        {
                            Console.WriteLine($"发送数据包失败: {sendResult}");
                            return false;
                        }

                        // 接收解码后的帧
                        var receiveResult = ffmpeg.avcodec_receive_frame(_codecContext, _frame);
                        if (receiveResult == 0)
                        {
                            // 成功解码出帧
                            return ProcessDecodedFrame();
                        }
                        else if (receiveResult == ffmpeg.AVERROR(ffmpeg.EAGAIN))
                        {
                            // 需要更多数据
                            return false;
                        }
                        else
                        {
                            Console.WriteLine($"接收帧失败: {receiveResult}");
                            return false;
                        }
                    }
                }
                finally
                {
                    ffmpeg.av_packet_free(&packet);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解码帧时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 处理解码后的帧
        /// </summary>
        private bool ProcessDecodedFrame()
        {
            try
            {
                int width = _frame->width;
                int height = _frame->height;

                // 如果尺寸改变，重新初始化转换器
                if (Width != width || Height != height)
                {
                    Width = width;
                    Height = height;
                    InitializeConverter();
                }

                // 转换为RGB格式
                if (_swsContext != null)
                {
                    // 设置RGB帧参数
                    _frameRGB->width = width;
                    _frameRGB->height = height;
                    _frameRGB->format = (int)AVPixelFormat.AV_PIX_FMT_RGB24;

                    // 分配缓冲区
                    int bufferSize = ffmpeg.av_image_get_buffer_size(AVPixelFormat.AV_PIX_FMT_RGB24, width, height, 1);
                    if (_buffer == null || _buffer.Length != bufferSize)
                    {
                        _buffer = new byte[bufferSize];
                    }

                    fixed (byte* bufferPtr = _buffer)
                    {
                        // 直接设置RGB帧的数据指针
                        _frameRGB->data[0] = bufferPtr;
                        _frameRGB->linesize[0] = width * 3; // RGB24每像素3字节

                        // 执行颜色空间转换
                        ffmpeg.sws_scale(_swsContext,
                            _frame->data, _frame->linesize, 0, height,
                            _frameRGB->data, _frameRGB->linesize);

                        // 更新帧缓冲
                        _frameBuffer.UpdateFrame(_buffer, width, height);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理解码帧时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 初始化颜色空间转换器
        /// </summary>
        private void InitializeConverter()
        {
            try
            {
                // 释放旧的转换器
                if (_swsContext != null)
                {
                    ffmpeg.sws_freeContext(_swsContext);
                }

                // 创建新的转换器
                _swsContext = ffmpeg.sws_getContext(
                    Width, Height, (AVPixelFormat)_frame->format,
                    Width, Height, AVPixelFormat.AV_PIX_FMT_RGB24,
                    ffmpeg.SWS_BILINEAR, null, null, null);

                if (_swsContext == null)
                {
                    Console.WriteLine("无法创建颜色空间转换器");
                }
                else
                {
                    Console.WriteLine($"颜色空间转换器初始化成功: {Width}x{Height}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化转换器时发生错误: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                if (_swsContext != null)
                {
                    ffmpeg.sws_freeContext(_swsContext);
                    _swsContext = null;
                }

                if (_frameRGB != null)
                {
                    var frameRGB = _frameRGB;
                    ffmpeg.av_frame_free(&frameRGB);
                    _frameRGB = null;
                }

                if (_frame != null)
                {
                    var frame = _frame;
                    ffmpeg.av_frame_free(&frame);
                    _frame = null;
                }

                if (_codecContext != null)
                {
                    var codecContext = _codecContext;
                    ffmpeg.avcodec_free_context(&codecContext);
                    _codecContext = null;
                }

                _frameBuffer?.Dispose();
                _disposed = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"释放解码器资源时发生错误: {ex.Message}");
            }
        }
    }
}
