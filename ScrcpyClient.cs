using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy客户端，用于连接和控制Android设备
    /// </summary>
    public class ScrcpyClient : IDisposable
    {
        private TcpClient _videoClient;
        private TcpClient _controlClient;
        private NetworkStream _videoStream;
        private NetworkStream _controlStream;
        private VideoDecoder _videoDecoder;
        private DeviceController _deviceController;
        private Process _serverProcess;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _videoReceiveTask;
        private bool _disposed;
        private bool _isConnected;

        // 设备信息
        private string _deviceName;
        private int _screenWidth;
        private int _screenHeight;

        /// <summary>
        /// 视频解码器
        /// </summary>
        public VideoDecoder VideoDecoder => _videoDecoder;

        /// <summary>
        /// 设备控制器
        /// </summary>
        public DeviceController DeviceController => _deviceController;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName => _deviceName;

        /// <summary>
        /// 屏幕宽度
        /// </summary>
        public int ScreenWidth => _screenWidth;

        /// <summary>
        /// 屏幕高度
        /// </summary>
        public int ScreenHeight => _screenHeight;

        /// <summary>
        /// 连接事件
        /// </summary>
        public event EventHandler<string> Connected;

        /// <summary>
        /// 断开连接事件
        /// </summary>
        public event EventHandler Disconnected;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<string> Error;

        public ScrcpyClient()
        {
            _videoDecoder = new VideoDecoder();
            _deviceController = new DeviceController();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <param name="deviceId">设备ID（可选，为空则连接第一个设备）</param>
        /// <param name="maxSize">最大分辨率</param>
        /// <param name="bitRate">视频比特率</param>
        /// <param name="port">端口号</param>
        public async Task<bool> ConnectAsync(string deviceId = null, int maxSize = 1080, int bitRate = 2000000, int port = ScrcpyProtocol.DEFAULT_PORT)
        {
            try
            {
                Console.WriteLine("开始连接设备...");

                // 启动scrcpy服务器
                if (!await StartServerAsync(deviceId, maxSize, bitRate, port))
                {
                    return false;
                }

                // 等待服务器启动并尝试连接
                Console.WriteLine("等待服务器启动...");
                //await Task.Delay(3000);

                // 尝试连接视频流，重试机制
                bool videoConnected = false;
                for (int i = 0; i < 5; i++)
                {
                    Console.WriteLine($"尝试连接视频流 (第{i + 1}次)...");
                    if (await ConnectVideoStreamAsync(port))
                    {
                        videoConnected = true;
                        break;
                    }
                    await Task.Delay(1000);
                }

                if (!videoConnected)
                {
                    Console.WriteLine("视频流连接失败，请检查：");
                    Console.WriteLine("1. 设备是否正确连接");
                    Console.WriteLine("2. scrcpy-server.jar是否存在");
                    Console.WriteLine("3. 端口转发是否成功");
                    return false;
                }

                // 连接控制流（使用同一个端口）
                if (!await ConnectControlStreamAsync(port))
                {
                    return false;
                }

                // 接收设备元数据
                if (!await ReceiveDeviceMetaAsync())
                {
                    return false;
                }

                // 初始化视频解码器
                if (!_videoDecoder.InitializeDecoder())
                {
                    Error?.Invoke(this, "初始化视频解码器失败");
                    return false;
                }

                // 设置设备控制器
                _deviceController.SetControlStream(_controlStream, _screenWidth, _screenHeight);

                // 开始接收视频数据
                _videoReceiveTask = Task.Run(() => ReceiveVideoDataAsync(_cancellationTokenSource.Token));

                _isConnected = true;
                Connected?.Invoke(this, _deviceName);
                Console.WriteLine($"成功连接到设备: {_deviceName} ({_screenWidth}x{_screenHeight})");

                return true;
            }
            catch (Exception ex)
            {
                Error?.Invoke(this, $"连接失败: {ex.Message}");
                Console.WriteLine($"连接失败: {ex.Message}");
                await DisconnectAsync();
                return false;
            }
        }

        /// <summary>
        /// 启动scrcpy服务器
        /// </summary>
        private async Task<bool> StartServerAsync(string deviceId, int maxSize, int bitRate, int port)
        {
            try
            {
                // 构建adb命令前缀
                var adbCommand = "adb";
                if (!string.IsNullOrEmpty(deviceId))
                {
                    adbCommand += $" -s {deviceId}";
                }

                // 1. 检查设备连接
                Console.WriteLine("检查设备连接...");
                Console.WriteLine("执行命令: adb devices");
                var checkDeviceProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = "devices",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };
                checkDeviceProcess.Start();
                var deviceOutput = await checkDeviceProcess.StandardOutput.ReadToEndAsync();
                var deviceError = await checkDeviceProcess.StandardError.ReadToEndAsync();
                checkDeviceProcess.WaitForExit();
                Console.WriteLine($"设备列表输出:\n{deviceOutput}");
                if (!string.IsNullOrEmpty(deviceError))
                {
                    Console.WriteLine($"设备列表错误:\n{deviceError}");
                }

                // 2. 推送scrcpy-server.jar到设备
                Console.WriteLine("推送scrcpy-server.jar到设备...");
                var pushCommand = $"{(string.IsNullOrEmpty(deviceId) ? "adb" : $"adb -s {deviceId}")} push scrcpy-server.jar /data/local/tmp/";
                Console.WriteLine($"执行命令: {pushCommand}");
                var pushProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = $"{(string.IsNullOrEmpty(deviceId) ? "" : $"-s {deviceId} ")}push scrcpy-server.jar /data/local/tmp/",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };
                pushProcess.Start();
                var pushOutput = await pushProcess.StandardOutput.ReadToEndAsync();
                var pushError = await pushProcess.StandardError.ReadToEndAsync();
                pushProcess.WaitForExit();

                Console.WriteLine($"推送输出:\n{pushOutput}");
                if (!string.IsNullOrEmpty(pushError))
                {
                    Console.WriteLine($"推送错误:\n{pushError}");
                }

                if (pushProcess.ExitCode != 0)
                {
                    Console.WriteLine($"推送失败，退出码: {pushProcess.ExitCode}");
                    // 继续尝试，可能文件已经存在
                }
                else
                {
                    Console.WriteLine("scrcpy-server.jar推送成功");
                }

                // 3. 设置反向端口转发
                // scrcpy服务器在设备上监听localabstract socket，我们需要将其转发到主机端口
                var scid = 0x17f3; // 使用与示例相同的scid
                var socketName = $"scrcpy_{scid:x8}";

                // 视频流端口转发
                Console.WriteLine($"设置视频流反向端口转发: localabstract:{socketName} -> tcp:{port}...");
                var reverseVideoCommand = $"{(string.IsNullOrEmpty(deviceId) ? "adb" : $"adb -s {deviceId}")} reverse localabstract:{socketName} tcp:{port}";
                Console.WriteLine($"执行命令: {reverseVideoCommand}");

                var reverseVideoProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = $"{(string.IsNullOrEmpty(deviceId) ? "" : $"-s {deviceId} ")}reverse localabstract:{socketName} tcp:{port}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };
                reverseVideoProcess.Start();
                var reverseVideoOutput = await reverseVideoProcess.StandardOutput.ReadToEndAsync();
                var reverseVideoError = await reverseVideoProcess.StandardError.ReadToEndAsync();
                reverseVideoProcess.WaitForExit();

                Console.WriteLine($"视频流反向端口转发输出:\n{reverseVideoOutput}");
                if (!string.IsNullOrEmpty(reverseVideoError))
                {
                    Console.WriteLine($"视频流反向端口转发错误:\n{reverseVideoError}");
                }

                if (reverseVideoProcess.ExitCode != 0)
                {
                    Console.WriteLine($"视频流反向端口转发失败，退出码: {reverseVideoProcess.ExitCode}");
                }
                else
                {
                    Console.WriteLine("视频流反向端口转发设置成功");
                }

                // 控制流端口转发（使用不同的socket名称）
                var controlSocketName = $"scrcpy_control_{scid:x8}";
                Console.WriteLine($"设置控制流反向端口转发: localabstract:{controlSocketName} -> tcp:{port + 1}...");
                var reverseControlCommand = $"{(string.IsNullOrEmpty(deviceId) ? "adb" : $"adb -s {deviceId}")} reverse localabstract:{controlSocketName} tcp:{port + 1}";
                Console.WriteLine($"执行命令: {reverseControlCommand}");

                var reverseControlProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = $"{(string.IsNullOrEmpty(deviceId) ? "" : $"-s {deviceId} ")}reverse localabstract:{controlSocketName} tcp:{port + 1}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };
                reverseControlProcess.Start();
                var reverseControlOutput = await reverseControlProcess.StandardOutput.ReadToEndAsync();
                var reverseControlError = await reverseControlProcess.StandardError.ReadToEndAsync();
                reverseControlProcess.WaitForExit();

                Console.WriteLine($"控制流反向端口转发输出:\n{reverseControlOutput}");
                if (!string.IsNullOrEmpty(reverseControlError))
                {
                    Console.WriteLine($"控制流反向端口转发错误:\n{reverseControlError}");
                }

                if (reverseControlProcess.ExitCode != 0)
                {
                    Console.WriteLine($"控制流反向端口转发失败，退出码: {reverseControlProcess.ExitCode}");
                }
                else
                {
                    Console.WriteLine("控制流反向端口转发设置成功");
                }

                // 4. 生成服务器参数
                var serverArgs = ScrcpyProtocol.GenerateServerArgs(maxSize, bitRate, false, scid);

                // 5. 启动服务器
                var serverCommand = $"{adbCommand} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server {ScrcpyProtocol.PROTOCOL_VERSION} {serverArgs}";
                Console.WriteLine($"启动服务器...");
                Console.WriteLine($"执行命令: {serverCommand}");

                _serverProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "cmd.exe",
                        Arguments = $"/c {serverCommand}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                _serverProcess.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        Console.WriteLine($"[服务器输出] {e.Data}");
                    }
                };

                _serverProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        Console.WriteLine($"[服务器错误] {e.Data}");
                    }
                };

                _serverProcess.Start();
                _serverProcess.BeginOutputReadLine();
                _serverProcess.BeginErrorReadLine();
                Console.WriteLine("Scrcpy服务器已启动");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动服务器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 等待视频流连接
        /// </summary>
        private async Task<bool> ConnectVideoStreamAsync(int port)
        {
            try
            {
                Console.WriteLine($"创建TCP服务器监听端口 {port} 等待视频流连接...");

                var listener = new TcpListener(IPAddress.Any, port);
                listener.Start();

                Console.WriteLine($"TCP服务器已启动，等待scrcpy服务器连接...");

                // 等待连接，设置超时
                var acceptTask = listener.AcceptTcpClientAsync();
                var timeoutTask = Task.Delay(10000); // 10秒超时

                var completedTask = await Task.WhenAny(acceptTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    listener.Stop();
                    Console.WriteLine("✗ 等待视频流连接超时");
                    return false;
                }

                _videoClient = await acceptTask;
                _videoStream = _videoClient.GetStream();
                listener.Stop();

                Console.WriteLine("✓ 视频流连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 等待视频流连接失败: {ex.Message}");
                Console.WriteLine($"  端口: {port}");
                Console.WriteLine($"  异常类型: {ex.GetType().Name}");
                return false;
            }
        }

        /// <summary>
        /// 等待控制流连接
        /// </summary>
        private async Task<bool> ConnectControlStreamAsync(int port)
        {
            try
            {
                Console.WriteLine($"创建TCP服务器监听端口 {port + 1} 等待控制流连接...");

                var listener = new TcpListener(IPAddress.Any, port + 1);
                listener.Start();

                Console.WriteLine($"TCP服务器已启动，等待scrcpy控制流连接...");

                // 等待连接，设置超时
                var acceptTask = listener.AcceptTcpClientAsync();
                var timeoutTask = Task.Delay(10000); // 10秒超时

                var completedTask = await Task.WhenAny(acceptTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    listener.Stop();
                    Console.WriteLine("✗ 等待控制流连接超时");
                    return false;
                }

                _controlClient = await acceptTask;
                _controlStream = _controlClient.GetStream();
                listener.Stop();

                Console.WriteLine("✓ 控制流连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 等待控制流连接失败: {ex.Message}");
                Console.WriteLine($"  端口: {port + 1}");
                Console.WriteLine($"  异常类型: {ex.GetType().Name}");
                return false;
            }
        }




        /// <summary>
        /// 接收设备元数据
        /// </summary>
        private async Task<bool> ReceiveDeviceMetaAsync()
        {
            try
            {
                // 读取设备元数据
                var buffer = new byte[1024];
                var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length);
                
                if (bytesRead > 0)
                {
                    var metaData = new byte[bytesRead];
                    Array.Copy(buffer, metaData, bytesRead);
                    
                    var (deviceName, width, height) = ScrcpyProtocol.ParseDeviceMeta(metaData);
                    _deviceName = deviceName;
                    _screenWidth = width;
                    _screenHeight = height;
                    
                    Console.WriteLine($"接收到设备元数据: {deviceName} ({width}x{height})");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收设备元数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 接收视频数据
        /// </summary>
        private async Task ReceiveVideoDataAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[65536]; // 64KB缓冲区
            
            try
            {
                while (!cancellationToken.IsCancellationRequested && _videoStream != null)
                {
                    var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    
                    if (bytesRead > 0)
                    {
                        // 解码视频帧
                        _videoDecoder.DecodeFrame(buffer, bytesRead);
                    }
                    else
                    {
                        // 连接断开
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收视频数据时发生错误: {ex.Message}");
                Error?.Invoke(this, $"视频接收错误: {ex.Message}");
            }
            
            Console.WriteLine("视频数据接收已停止");
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (_disposed) return;

            try
            {
                _isConnected = false;

                // 取消任务
                _cancellationTokenSource?.Cancel();

                // 等待视频接收任务完成
                if (_videoReceiveTask != null)
                {
                    await _videoReceiveTask;
                }

                // 关闭网络连接
                _videoStream?.Close();
                _controlStream?.Close();
                _videoClient?.Close();
                _controlClient?.Close();

                // 停止服务器进程
                if (_serverProcess != null && !_serverProcess.HasExited)
                {
                    _serverProcess.Kill();
                    _serverProcess.WaitForExit(5000);
                }

                // 清理反向端口转发
                await CleanupReversePortForwardingAsync();

                Disconnected?.Invoke(this, EventArgs.Empty);
                Console.WriteLine("已断开连接");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"断开连接时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理反向端口转发
        /// </summary>
        private async Task CleanupReversePortForwardingAsync()
        {
            try
            {
                var scid = 0x17f3;
                var socketName = $"scrcpy_{scid:x8}";

                var cleanupProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "adb",
                        Arguments = $"reverse --remove localabstract:{socketName}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                cleanupProcess.Start();
                await cleanupProcess.StandardOutput.ReadToEndAsync();
                cleanupProcess.WaitForExit();

                Console.WriteLine("反向端口转发已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理反向端口转发时发生错误: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            DisconnectAsync().Wait();
            
            _videoDecoder?.Dispose();
            _deviceController?.Dispose();
            _cancellationTokenSource?.Dispose();
            _serverProcess?.Dispose();
            
            _disposed = true;
        }
    }
}
