using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGame
{
    /// <summary>
    /// Scrcpy客户端，用于连接和控制Android设备
    /// </summary>
    public class ScrcpyClient : IDisposable
    {
        private TcpClient _videoClient;
        private TcpClient _controlClient;
        private NetworkStream _videoStream;
        private NetworkStream _controlStream;
        private VideoDecoder _videoDecoder;
        private DeviceController _deviceController;
        private Process _serverProcess;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _videoReceiveTask;
        private bool _disposed;
        private bool _isConnected;

        // 设备信息
        private string _deviceName;
        private int _screenWidth;
        private int _screenHeight;

        /// <summary>
        /// 视频解码器
        /// </summary>
        public VideoDecoder VideoDecoder => _videoDecoder;

        /// <summary>
        /// 设备控制器
        /// </summary>
        public DeviceController DeviceController => _deviceController;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName => _deviceName;

        /// <summary>
        /// 屏幕宽度
        /// </summary>
        public int ScreenWidth => _screenWidth;

        /// <summary>
        /// 屏幕高度
        /// </summary>
        public int ScreenHeight => _screenHeight;

        /// <summary>
        /// 连接事件
        /// </summary>
        public event EventHandler<string> Connected;

        /// <summary>
        /// 断开连接事件
        /// </summary>
        public event EventHandler Disconnected;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<string> Error;

        public ScrcpyClient()
        {
            _videoDecoder = new VideoDecoder();
            _deviceController = new DeviceController();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <param name="deviceId">设备ID（可选，为空则连接第一个设备）</param>
        /// <param name="maxSize">最大分辨率</param>
        /// <param name="bitRate">视频比特率</param>
        /// <param name="port">端口号</param>
        public async Task<bool> ConnectAsync(string deviceId = null, int maxSize = 1080, int bitRate = 2000000, int port = ScrcpyProtocol.DEFAULT_PORT)
        {
            try
            {
                Console.WriteLine("开始连接设备...");

                // 启动scrcpy服务器
                if (!await StartServerAsync(deviceId, maxSize, bitRate, port))
                {
                    return false;
                }

                // 等待服务器启动
                await Task.Delay(2000);

                // 连接视频流
                if (!await ConnectVideoStreamAsync(port))
                {
                    return false;
                }

                // 连接控制流
                if (!await ConnectControlStreamAsync(port))
                {
                    return false;
                }

                // 接收设备元数据
                if (!await ReceiveDeviceMetaAsync())
                {
                    return false;
                }

                // 初始化视频解码器
                if (!_videoDecoder.InitializeDecoder())
                {
                    Error?.Invoke(this, "初始化视频解码器失败");
                    return false;
                }

                // 设置设备控制器
                _deviceController.SetControlStream(_controlStream, _screenWidth, _screenHeight);

                // 开始接收视频数据
                _videoReceiveTask = Task.Run(() => ReceiveVideoDataAsync(_cancellationTokenSource.Token));

                _isConnected = true;
                Connected?.Invoke(this, _deviceName);
                Console.WriteLine($"成功连接到设备: {_deviceName} ({_screenWidth}x{_screenHeight})");

                return true;
            }
            catch (Exception ex)
            {
                Error?.Invoke(this, $"连接失败: {ex.Message}");
                Console.WriteLine($"连接失败: {ex.Message}");
                await DisconnectAsync();
                return false;
            }
        }

        /// <summary>
        /// 启动scrcpy服务器
        /// </summary>
        private Task<bool> StartServerAsync(string deviceId, int maxSize, int bitRate, int port)
        {
            try
            {
                // 生成服务器参数
                var serverArgs = ScrcpyProtocol.GenerateServerArgs(maxSize, bitRate, false);

                // 构建adb命令
                var adbCommand = "adb";
                if (!string.IsNullOrEmpty(deviceId))
                {
                    adbCommand += $" -s {deviceId}";
                }

                // 推送scrcpy-server.jar到设备（如果需要）
                var pushCommand = $"{adbCommand} push scrcpy-server.jar /data/local/tmp/";

                // 启动服务器
                var serverCommand = $"{adbCommand} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server {ScrcpyProtocol.PROTOCOL_VERSION} {serverArgs}";

                Console.WriteLine($"启动服务器命令: {serverCommand}");

                _serverProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "cmd.exe",
                        Arguments = $"/c {serverCommand}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                _serverProcess.Start();
                Console.WriteLine("Scrcpy服务器已启动");

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动服务器失败: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 连接视频流
        /// </summary>
        private async Task<bool> ConnectVideoStreamAsync(int port)
        {
            try
            {
                _videoClient = new TcpClient();
                await _videoClient.ConnectAsync(IPAddress.Loopback, port);
                _videoStream = _videoClient.GetStream();
                Console.WriteLine("视频流连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接视频流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 连接控制流
        /// </summary>
        private async Task<bool> ConnectControlStreamAsync(int port)
        {
            try
            {
                _controlClient = new TcpClient();
                await _controlClient.ConnectAsync(IPAddress.Loopback, port + 1);
                _controlStream = _controlClient.GetStream();
                Console.WriteLine("控制流连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接控制流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 接收设备元数据
        /// </summary>
        private async Task<bool> ReceiveDeviceMetaAsync()
        {
            try
            {
                // 读取设备元数据
                var buffer = new byte[1024];
                var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length);
                
                if (bytesRead > 0)
                {
                    var metaData = new byte[bytesRead];
                    Array.Copy(buffer, metaData, bytesRead);
                    
                    var (deviceName, width, height) = ScrcpyProtocol.ParseDeviceMeta(metaData);
                    _deviceName = deviceName;
                    _screenWidth = width;
                    _screenHeight = height;
                    
                    Console.WriteLine($"接收到设备元数据: {deviceName} ({width}x{height})");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收设备元数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 接收视频数据
        /// </summary>
        private async Task ReceiveVideoDataAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[65536]; // 64KB缓冲区
            
            try
            {
                while (!cancellationToken.IsCancellationRequested && _videoStream != null)
                {
                    var bytesRead = await _videoStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    
                    if (bytesRead > 0)
                    {
                        // 解码视频帧
                        _videoDecoder.DecodeFrame(buffer, bytesRead);
                    }
                    else
                    {
                        // 连接断开
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收视频数据时发生错误: {ex.Message}");
                Error?.Invoke(this, $"视频接收错误: {ex.Message}");
            }
            
            Console.WriteLine("视频数据接收已停止");
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (_disposed) return;

            try
            {
                _isConnected = false;
                
                // 取消任务
                _cancellationTokenSource?.Cancel();
                
                // 等待视频接收任务完成
                if (_videoReceiveTask != null)
                {
                    await _videoReceiveTask;
                }
                
                // 关闭网络连接
                _videoStream?.Close();
                _controlStream?.Close();
                _videoClient?.Close();
                _controlClient?.Close();
                
                // 停止服务器进程
                if (_serverProcess != null && !_serverProcess.HasExited)
                {
                    _serverProcess.Kill();
                    _serverProcess.WaitForExit(5000);
                }
                
                Disconnected?.Invoke(this, EventArgs.Empty);
                Console.WriteLine("已断开连接");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"断开连接时发生错误: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            DisconnectAsync().Wait();
            
            _videoDecoder?.Dispose();
            _deviceController?.Dispose();
            _cancellationTokenSource?.Dispose();
            _serverProcess?.Dispose();
            
            _disposed = true;
        }
    }
}
