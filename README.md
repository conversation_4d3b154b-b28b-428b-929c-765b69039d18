# Scrcpy C# 客户端

这是一个使用C#实现的Scrcpy客户端，可以连接Android设备并进行屏幕镜像和设备控制。

## 功能特性

- ✅ 连接Android设备
- ✅ 实时视频流接收和解码（使用FFmpeg.AutoGen）
- ✅ 获取最新视频帧
- ✅ 设备控制（触摸、按键、文本输入等）
- ✅ 屏幕截图保存
- ✅ 异步操作支持

## 系统要求

- .NET Framework 4.7.2 或更高版本
- Windows 操作系统
- Android设备（启用USB调试）
- ADB工具
- FFmpeg库文件

## 依赖包

- FFmpeg.AutoGen 7.1.1
- OpenCvSharp4 4.11.0（可选，用于图像处理）

## 使用方法

### 1. 基本连接

```csharp
using (var client = new ScrcpyClient())
{
    // 连接到设备
    bool connected = await client.ConnectAsync(
        deviceId: null,     // 自动选择第一个设备
        maxSize: 1080,      // 最大分辨率1080p
        bitRate: 2000000    // 2Mbps比特率
    );

    if (connected)
    {
        Console.WriteLine($"已连接到设备: {client.DeviceName}");
        Console.WriteLine($"屏幕尺寸: {client.ScreenWidth}x{client.ScreenHeight}");
    }
}
```

### 2. 获取视频帧

```csharp
var frameBuffer = client.VideoDecoder.FrameBuffer;

// 检查是否有可用帧
if (frameBuffer.HasFrame)
{
    // 获取当前帧的Bitmap
    using (var bitmap = frameBuffer.GetCurrentFrame())
    {
        // 处理图像...
    }
    
    // 或获取原始RGB数据
    var frameData = frameBuffer.GetCurrentFrameData();
    
    // 保存截图
    frameBuffer.SaveCurrentFrame("screenshot.png");
}
```

### 3. 设备控制

```csharp
var controller = client.DeviceController;

// 点击屏幕
await controller.ClickAsync(x: 500, y: 300);

// 滑动
await controller.SwipeAsync(
    startX: 100, startY: 500,
    endX: 900, endY: 500,
    duration: 500
);

// 按键操作
await controller.PressBackAsync();
await controller.PressHomeAsync();
await controller.PressKeyAsync(AndroidKeyCode.KEYCODE_ENTER);

// 文本输入
await controller.InputTextAsync("Hello World!");

// 屏幕电源控制
await controller.SetScreenPowerModeAsync(ScrcpyProtocol.ScreenPowerMode.NORMAL);
```

### 4. 事件处理

```csharp
client.Connected += (sender, deviceName) =>
{
    Console.WriteLine($"已连接到设备: {deviceName}");
};

client.Disconnected += (sender, e) =>
{
    Console.WriteLine("设备已断开连接");
};

client.Error += (sender, error) =>
{
    Console.WriteLine($"错误: {error}");
};
```

## 核心类说明

### ScrcpyClient
主要的客户端类，负责：
- 启动scrcpy服务器
- 管理网络连接
- 协调视频解码和设备控制

### VideoDecoder
视频解码器，使用FFmpeg.AutoGen：
- 解码H264/H265视频流
- 转换为RGB格式
- 管理帧缓冲

### FrameBuffer
帧缓冲管理器：
- 存储最新的视频帧
- 提供线程安全的帧访问
- 支持截图保存

### DeviceController
设备控制器：
- 发送触摸事件
- 发送按键事件
- 文本输入
- 屏幕电源控制

### ScrcpyProtocol
协议定义：
- 消息格式定义
- 协议常量
- 消息创建工具

## 安装和配置

### 1. 准备scrcpy-server.jar

从scrcpy官方仓库下载或编译scrcpy-server.jar文件，放在项目根目录。

### 2. 安装ADB

确保系统PATH中包含adb命令，或将adb.exe放在项目目录中。

### 3. FFmpeg库

下载FFmpeg库文件，确保程序能找到相关的DLL文件。

### 4. 设备准备

- 启用开发者选项
- 启用USB调试
- 连接设备并授权调试

## 快速开始

1. **编译项目**
   ```bash
   dotnet build AutoGame.csproj
   ```

2. **运行演示程序**
   ```bash
   dotnet run
   ```

3. **在您的项目中使用**
   ```csharp
   // 简单使用示例
   var example = new ScrcpyExample();

   // 连接设备
   bool connected = await example.ConnectToDeviceAsync();
   if (connected)
   {
       // 获取截图
       using (var screenshot = example.GetScreenshot())
       {
           // 处理截图...
       }

       // 点击屏幕
       await example.ClickAsync(500, 300);

       // 断开连接
       await example.DisconnectAsync();
   }
   ```

## 注意事项

1. **权限要求**：需要在Android设备上启用USB调试
2. **网络端口**：默认使用端口27183（视频）和27184（控制）
3. **性能考虑**：视频解码会消耗一定CPU资源
4. **线程安全**：FrameBuffer类是线程安全的
5. **资源管理**：使用using语句确保资源正确释放

## 故障排除

### 连接失败
- 检查设备是否连接并授权
- 确认adb命令可用：`adb devices`
- 检查scrcpy-server.jar文件是否存在

### 视频解码失败
- 确认FFmpeg库文件正确安装
- 检查设备是否支持H264编码
- 尝试降低分辨率和比特率

### 控制无响应
- 确认设备允许模拟输入
- 检查坐标是否在屏幕范围内
- 确认控制流连接正常

## 示例项目

参考Program.cs中的完整示例，展示了如何：
- 连接设备
- 获取视频帧
- 进行设备控制
- 处理错误和断开连接

## 许可证

本项目基于MIT许可证开源。
